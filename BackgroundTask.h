#pragma once
#include <vector>
#include <thread>
#include <functional>
using namespace std;

class BackgroundTask
{
public:
    vector<int> &arr; // 引用成员变量
    BackgroundTask(vector<int> &a) : arr(a) {};
    void operator()(vector<int> &a);

    void do_lengthy_work(int a);
};

class ThreadGuard
{
    thread &t;

public:
    explicit ThreadGuard(thread &t_) : t(t_) {};
    ~ThreadGuard()
    {
        if (t.joinable())
        {
            t.join();
        }
    }
    ThreadGuard(ThreadGuard const &) = delete;
    ThreadGuard &operator=(ThreadGuard const &) = delete;
};

/**
 * 用于初始化的，只锁一次的锁例子
 */
class OnceInitDemo
{
    vector<int> connection_data;
    // once_flag 同步原语，用于确保某个函数或代码块在多线程环境中只被执行一次。
    std::once_flag onceFlag_connection;
    void connection_open(vector<int> &v)
    {
        connection_data = move(v);
    }

public:
    void orderSend(const vector<int> &v, char c)
    {
        /* 用call_once只锁一次
           例如每次发送数据，要检查是否打开了通讯通道，检查的时候保证数据不变量，需要上锁。
           如果用普通的锁，线程到此处就卡成序列化了
         */
        call_once(onceFlag_connection, &OnceInitDemo::connection_open, this, v);
        cout << "\n模拟发送数据：\n";
        for (auto i : v)
        {
            cout << c << i << " ";
        }
    }
};